<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;

class ContactController extends Controller
{
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|max:255',
            'email' => 'required|email',
            'phone' => 'nullable|string|max:20',
            'message' => 'required',
            'service' => 'nullable|string'
        ]);

        // Send email using template
        Mail::send('emails.contact', ['data' => $validated], function($message) use ($validated) {
            $message->to('<EMAIL>')
                    ->subject(__('messages.contact_form_subject', ['name' => $validated['name']]))
                    ->from($validated['email'], $validated['name']);
        });

        return redirect()->back()->with('success', __('messages.message_success'));
    }
}
