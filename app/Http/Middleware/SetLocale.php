<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class SetLocale
{
    public function handle(Request $request, Closure $next)
    {
        if (session()->has('locale')) {
            app()->setLocale(session()->get('locale'));
        } else {
            // Use the default locale from config
            $locale = config('app.locale');
            app()->setLocale($locale);
            session()->put('locale', $locale);
        }
        return $next($request);
    }
}
