.clients-section {
    background: linear-gradient(to bottom, #f8f9fa, #ffffff);
}

.shadow-hover {
    transition: all 0.3s ease;
}

.shadow-hover:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 128, 0.1) !important;
}

.client-card .card {
    border-radius: 15px;
    background: #ffffff;
}

.badge.bg-turquoise-light {
    background-color: rgba(0, 0, 128, 0.1);
    font-weight: 500;
    font-size: 0.9rem;
    border-radius: 30px;
}

.client-logo {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.client-info img {
    object-fit: cover;
}

.btn-outline-turquoise {
    padding: 12px 30px;
    border-radius: 30px;
    font-weight: 500;
    transition: all 0.3s ease;
}
/* Gradients */
.bg-gradient-turquoise {
    background: linear-gradient(135deg, #000080 0%, #40E0D0 100%);
}

/* Animations */
.hover-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 1rem 3rem rgba(0,0,0,.175)!important;
}

/* Custom Utilities */
.py-6 {
    padding-top: 4.5rem;
    padding-bottom: 4.5rem;
}

.transform-perspective-right {
    transform: perspective(1000px) rotateY(-15deg);
    transition: transform 0.3s ease;
}

.transform-perspective-right:hover {
    transform: perspective(1000px) rotateY(0);
}

/* Background Patterns */
.bg-pattern-dots {
    background-image: radial-gradient(rgba(255,255,255,0.2) 2px, transparent 2px);
    background-size: 30px 30px;
}

/* Stats Counter Animation */
.counter-value {
    transition: all 0.3s ease;
}

/* Service Cards */
.service-card {
    border-radius: 1rem;
    border: none;
    overflow: hidden;
}

.service-icon {
    width: 64px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 16px;
    background: rgba(64, 224, 208, 0.1);
}

/* Testimonial Cards */
.testimonial-card {
    background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 20px;
    border: none;
}

/* Custom Shadows */
.shadow-soft {
    box-shadow: 0 0.5rem 1.5rem rgba(0,0,0,.05);
}

.bg-turquoise {
    background-color: #40E0D0;
}

.text-turquoise {
    color: #40E0D0;
}

.rounded-lg {
    border-radius: 1rem !important;
}
