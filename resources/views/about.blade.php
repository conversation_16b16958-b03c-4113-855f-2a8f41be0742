@extends('layouts.app')

@section('title', __('messages.about_meta_title'))

@section('meta_description', __('messages.about_meta_description'))

@section('meta_keywords', __('messages.about_meta_keywords'))

@section('content')
    <!-- Page Header -->
    <header class="bg-turquoise-light py-5">
        <div class="container">
            <h1 class="display-4 fw-bold text-dark">{{ __('messages.about_us') }}</h1>
            <p class="lead">{{ __('messages.your_trusted_partner') }}</p>
        </div>
    </header>

    <!-- About Content -->
    <section class="py-5">
        <div class="container">
            <!-- Company Overview -->
            <div class="row align-items-center mb-5">
                <div class="col-lg-6">
                    <h2 class="mb-4">{{ __('messages.our_story') }}</h2>
                    <p>{{ __('messages.our_story_text_1') }}</p>
                    <p>{{ __('messages.our_story_text_2') }}</p>
                </div>
                <div class="col-lg-6">
                    <img src="{{ asset('images/about/team-meeting.jpg') }}"
                         alt="LianTaraz Professional Team Meeting"
                         class="img-fluid rounded shadow"
                         loading="lazy"
                         width="600"
                         height="400">
                </div>
            </div>

            <!-- Mission, Vision, Values -->
            <div class="row mb-5">
                <div class="col-md-4">
                    <div class="text-center">
                        <div class="icon-box bg-turquoise-light rounded-circle p-3 mx-auto mb-3" style="width: 80px; height: 80px;">
                            <i class="fas fa-bullseye fa-2x text-turquoise"></i>
                        </div>
                        <h3 class="h4 text-turquoise">{{ __('messages.our_mission') }}</h3>
                        <p>{{ __('messages.our_mission_text') }}</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <div class="icon-box bg-turquoise-light rounded-circle p-3 mx-auto mb-3" style="width: 80px; height: 80px;">
                            <i class="fas fa-eye fa-2x text-turquoise"></i>
                        </div>
                        <h3 class="h4 text-turquoise">{{ __('messages.our_vision') }}</h3>
                        <p>{{ __('messages.our_vision_text') }}</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <div class="icon-box bg-turquoise-light rounded-circle p-3 mx-auto mb-3" style="width: 80px; height: 80px;">
                            <i class="fas fa-heart fa-2x text-turquoise"></i>
                        </div>
                        <h3 class="h4 text-turquoise">{{ __('messages.our_values') }}</h3>
                        <p>{{ __('messages.our_values_text') }}</p>
                    </div>
                </div>
            </div>

            <!-- Our Services -->
            <div class="row mb-5" data-animate="fade-in-up">
                <div class="col-12 text-center mb-4">
                    <h2 class="display-5 fw-bold">{{ __('messages.our_expertise') }}</h2>
                    <p class="lead text-muted">{{ __('messages.comprehensive_solutions') }}</p>
                </div>

                <!-- Services Cards -->
                @foreach([
                    [
                        'icon' => 'building',
                        'title' => 'business_commercial_services_title',
                        'text' => 'business_commercial_services_text'
                    ],
                    [
                        'icon' => 'file-invoice-dollar',
                        'title' => 'financial_commercial_services_title',
                        'text' => 'financial_commercial_services_text'
                    ],
                    [
                        'icon' => 'truck-moving',
                        'title' => 'domestic_international_transport_title',
                        'text' => 'domestic_international_transport_text'
                    ],
                    [
                        'icon' => 'chart-line',
                        'title' => 'investment_capital_title',
                        'text' => 'investment_capital_text'
                    ],
                    [
                        'icon' => 'users',
                        'title' => 'hr_supply_title',
                        'text' => 'hr_supply_text'
                    ],
                    [
                        'icon' => 'shield-alt',
                        'title' => 'financial_reporting_title',
                        'text' => 'financial_reporting_text'
                    ]
                ] as $service)
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card h-100 border-0 shadow-sm hover-card">
                            <div class="card-body p-4">
                                <div class="service-icon bg-turquoise-light rounded-circle mb-4">
                                    <i class="fas fa-{{ $service['icon'] }} fa-2x text-turquoise"></i>
                                </div>
                                <h4 class="card-title h5 mb-3">{{ __('messages.' . $service['title']) }}</h4>
                                <p class="card-text text-muted">{{ __('messages.' . $service['text']) }}</p>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Call to Action -->
            <div class="text-center">
                <a href="{{ route('contact') }}" class="btn btn-turquoise btn-lg px-5">
                    <i class="fas fa-handshake me-2"></i>{{ __('messages.work_with_us') }}
                </a>
            </div>
        </div>
    </section>
@endsection

@push('styles')
<style>
    .bg-turquoise-light {
        background-color: rgba(64, 224, 208, 0.1);
    }

    .text-turquoise {
        color: #40E0D0;
    }

    .btn-turquoise {
        background-color: #40E0D0;
        border-color: #40E0D0;
        color: white;
    }

    .btn-turquoise:hover {
        background-color: #3AC5B7;
        border-color: #3AC5B7;
        color: white;
    }

    .service-icon {
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .card {
        transition: transform 0.3s ease;
    }

    .card:hover {
        transform: translateY(-5px);
    }
</style>
@endpush
