@extends('layouts.app')

@section('title', __('messages.contact_meta_title'))

@section('meta_description', __('messages.contact_meta_description'))

@section('meta_keywords', __('messages.contact_meta_keywords'))

@section('content')
    <!-- Page Header -->
    <header class="bg-turquoise-light py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <h1 class="display-4 fw-bold text-dark mb-2">{{ __('messages.contact_us') }}</h1>
                    <p class="lead mb-0">{{ __('messages.contact_description') }}</p>
                </div>
                <div class="col-lg-6 text-lg-end">

                </div>
            </div>
        </div>
    </header>

    <!-- Contact Content -->
    <section class="py-5">
        <div class="container">
            <div class="row g-4">
                <!-- Contact Information Cards -->
                <div class="col-lg-4 order-lg-2">
                    <!-- Quick Contact Card -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-body p-4">
                            <h3 class="h4 mb-4">{{ __('messages.get_in_touch') }}</h3>
                            <div class="d-flex align-items-center mb-3">
                                <div class="icon-box bg-turquoise-light rounded-circle p-3 me-3">
                                    <i class="fas fa-phone text-turquoise"></i>
                                </div>
                                <div>
                                    <p class="small text-muted mb-0">{{ __('messages.phone') }}</p>
                                    <a href="tel:+987791002036" class="text-dark text-decoration-none h5 mb-0" dir="ltr">+98 77 9100 2036</a>
                                </div>
                            </div>
                            <div class="d-flex align-items-center mb-3">
                                <div class="icon-box bg-turquoise-light rounded-circle p-3 me-3">
                                    <i class="fas fa-envelope text-turquoise"></i>
                                </div>
                                <div>
                                    <p class="small text-muted mb-0">{{ __('messages.email') }}</p>
                                    <a href="mailto:<EMAIL>" class="text-dark text-decoration-none h5 mb-0" dir="ltr"><EMAIL></a>
                                </div>
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="icon-box bg-turquoise-light rounded-circle p-3 me-3">
                                    <i class="fas fa-map-marker-alt text-turquoise"></i>
                                </div>
                                <div>
                                    <p class="small text-muted mb-0">{{ __('messages.address') }}</p>
                                    <h5 class="mb-0">
                                        {{ __('messages.address_line1') }}<br>
                                        {{ __('messages.address_line2') }}<br>
                                        {{ __('messages.address_line3') }}
                                    </h5>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Business Hours Card -->
                    <div class="card shadow-sm mb-4">
                        <div class="card-body p-4">
                            <h3 class="h4 mb-4">{{ __('messages.office_hours') }}</h3>
                            <div class="d-flex justify-content-between mb-2">
                                <span>{{ __('messages.weekdays') }}</span>
                                <span class="text-turquoise">8:00 - 16:00</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>{{ __('messages.weekends') }}</span>
                                <span class="text-danger">{{ __('messages.closed') }}</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>{{ __('messages.holidays') }}</span>
                                <span class="text-danger">{{ __('messages.closed') }}</span>
                            </div>

                        </div>
                    </div>

                    <!-- Social Media Card -->
                    <div class="card shadow-sm">
                        <div class="card-body p-4">
                            <h3 class="h4 mb-4">{{ __('messages.social_media') }}</h3>
                            <a href="https://wa.me/989171234567" class="btn btn-outline-success w-100 mb-3">
                                <i class="fab fa-whatsapp me-2"></i>{{ __('messages.request_callback') }}
                            </a>
                            <a href="#" class="btn btn-outline-primary w-100 mb-3">
                                <i class="fas fa-calendar-alt me-2"></i>{{ __('messages.schedule_consultation') }}
                            </a>
                            <a href="#" class="btn btn-outline-info w-100">
                                <i class="fas fa-download me-2"></i>{{ __('messages.download_brochure') }}
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Contact Form -->
                <div class="col-lg-8 order-lg-1">
                    <div class="card shadow-sm">
                        <div class="card-body p-4">
                            <h2 class="h3 mb-4">{{ __('messages.send_message') }}</h2>
                            @if(session('success'))
                                <div class="alert alert-success alert-dismissible fade show" role="alert">
                                    {{ __('messages.message_success') }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            @endif
                            <form action="{{ route('contact.store') }}" method="POST" class="row g-3">
                                @csrf
                                <div class="col-md-6">
                                    <label for="name" class="form-label">{{ __('messages.your_name') }}</label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ __('messages.required_field') }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-6">
                                    <label for="email" class="form-label">{{ __('messages.your_email') }}</label>
                                    <input type="email" class="form-control @error('email') is-invalid @enderror" id="email" name="email" required>
                                    @error('email')
                                        <div class="invalid-feedback">{{ __('messages.invalid_email') }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-6">
                                    <label for="phone" class="form-label">{{ __('messages.phone') }}</label>
                                    <input type="tel" class="form-control @error('phone') is-invalid @enderror" id="phone" name="phone">
                                    @error('phone')
                                        <div class="invalid-feedback">{{ __('messages.invalid_phone') }}</div>
                                    @enderror
                                </div>
                                <div class="col-md-6">
                                    <label for="service" class="form-label">{{ __('messages.service_interest') }}</label>
                                    <select class="form-select" id="service" name="service">
                                        <option value="">{{ __('messages.select_service') }}</option>
                                        <option value="business">{{ __('messages.business_commercial_services') }}</option>
                                        <option value="financial">{{ __('messages.financial_commercial_services') }}</option>
                                        <option value="transport">{{ __('messages.domestic_international_transport') }}</option>
                                        <option value="investment">{{ __('messages.investment_capital') }}</option>
                                        <option value="hr">{{ __('messages.hr_supply') }}</option>
                                        <option value="software">{{ __('messages.financial_reporting') }}</option>
                                    </select>
                                </div>
                                <div class="col-12">
                                    <label for="message" class="form-label">{{ __('messages.your_message') }}</label>
                                    <textarea class="form-control @error('message') is-invalid @enderror" id="message" name="message" rows="5" required></textarea>
                                    @error('message')
                                        <div class="invalid-feedback">
                                            @if($message->min)
                                                {{ __('messages.message_min_length') }}
                                            @elseif($message->max)
                                                {{ __('messages.message_max_length') }}
                                            @else
                                                {{ __('messages.required_field') }}
                                            @endif
                                        </div>
                                    @enderror
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-turquoise">
                                        {{ __('messages.send_message') }}
                                        <i class="fas fa-paper-plane ms-2"></i>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Map Section -->
                    <div class="mt-4">
                        <h3 class="h4 mb-3">{{ __('messages.our_location') }}</h3>
                        <div class="ratio ratio-16x9">
                            <iframe
                                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3539.825534958592!2d50.83659287537897!3d28.968095274879837!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3fb3f5d51c5cc5d1%3A0x0!2zMjjCsDU4JzA1LjEiTiA1MMKwNTAnMTkuMiJF!5e0!3m2!1sen!2s!4v1709729844037!5m2!1sen!2s"
                                style="border:0;"
                                allowfullscreen=""
                                loading="lazy"
                                referrerpolicy="no-referrer-when-downgrade">
                            </iframe>
                        </div>
                        <div class="mt-3">
                            <a href="https://maps.google.com/?q=28.968095,50.836593"
                               class="btn btn-outline-secondary me-2"
                               target="_blank">
                                <i class="fas fa-map-marked-alt me-2"></i>{{ __('messages.view_on_map') }}
                            </a>
                            <a href="https://www.google.com/maps/dir/?api=1&destination=28.968095,50.836593"
                               class="btn btn-outline-secondary"
                               target="_blank">
                                <i class="fas fa-directions me-2"></i>{{ __('messages.get_directions') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@push('styles')
<style>
    .icon-box {
        width: 45px;
        height: 45px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .bg-turquoise-light {
        background-color: rgba(64, 224, 208, 0.1);
    }

    .text-turquoise {
        color: #40E0D0;
    }

    .btn-turquoise {
        background-color: #40E0D0;
        border-color: #40E0D0;
        color: white;
    }

    .btn-turquoise:hover {
        background-color: #3AC5B7;
        border-color: #3AC5B7;
        color: white;
    }
</style>
@endpush

