@extends('layouts.app')

@section('content')
<div class="coming-soon min-vh-100 d-flex align-items-center position-relative overflow-hidden">
    <!-- Animated Background -->
    <div class="position-absolute w-100 h-100" style="background: linear-gradient(135deg, #000080 0%, #40E0D0 100%); z-index: -2;"></div>
    <div class="position-absolute w-100 h-100" style="background: url('{{ asset('images/pattern-grid.png') }}') repeat; opacity: 0.1; z-index: -1;"></div>

    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center">
                <!-- Logo -->
                <img src="{{ asset('images/logo-white.png') }}" alt="LianTaraz" class="mb-5" style="max-height: 80px;">
                
                <!-- Main Content -->
                <h1 class="display-2 fw-bold text-white mb-4" data-animate="fade-in-up">
                    به زودی با خدماتی جدید
                    <span class="position-relative">
                        در خدمت شما
                        <svg class="position-absolute start-0 bottom-0 w-100" style="height: 8px;">
                            <path d="M0 7 Q 25 0 50 7 Q 75 14 100 7" fill="none" stroke="#40E0D0" stroke-width="2"/>
                        </svg>
                    </span>
                    خواهیم بود
                </h1>
                
                <p class="lead text-white-75 mb-5" data-animate="fade-in-up delay-1">
                    وب‌سایت جدید لیان طراز در حال آماده‌سازی است. به زودی با امکانات و خدمات جدید در خدمت شما خواهیم بود.
                </p>

                <!-- Countdown Timer -->
                <div class="row g-4 mb-5" data-animate="fade-in-up delay-2">
                    <div class="col-3">
                        <div class="bg-white bg-opacity-10 rounded-4 p-3 text-white">
                            <h2 class="countdown-days display-4 fw-bold mb-0">00</h2>
                            <p class="mb-0">روز</p>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="bg-white bg-opacity-10 rounded-4 p-3 text-white">
                            <h2 class="countdown-hours display-4 fw-bold mb-0">00</h2>
                            <p class="mb-0">ساعت</p>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="bg-white bg-opacity-10 rounded-4 p-3 text-white">
                            <h2 class="countdown-minutes display-4 fw-bold mb-0">00</h2>
                            <p class="mb-0">دقیقه</p>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="bg-white bg-opacity-10 rounded-4 p-3 text-white">
                            <h2 class="countdown-seconds display-4 fw-bold mb-0">00</h2>
                            <p class="mb-0">ثانیه</p>
                        </div>
                    </div>
                </div>

                <!-- Newsletter Signup -->
                <div class="row justify-content-center" data-animate="fade-in-up delay-3">
                    <div class="col-md-8">
                        <form class="mb-5">
                            <div class="input-group input-group-lg">
                                <input type="email" class="form-control" placeholder="ایمیل خود را وارد کنید" required>
                                <button class="btn btn-light" type="submit">
                                    <i class="fas fa-paper-plane me-2"></i>مطلع شوید
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Social Links -->
                <div class="d-flex justify-content-center gap-3" data-animate="fade-in-up delay-4">
                    <a href="#" class="btn btn-light btn-lg rounded-circle">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <a href="#" class="btn btn-light btn-lg rounded-circle">
                        <i class="fab fa-telegram"></i>
                    </a>
                    <a href="#" class="btn btn-light btn-lg rounded-circle">
                        <i class="fab fa-whatsapp"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set the launch date (example: 30 days from now)
    const launchDate = new Date();
    launchDate.setDate(launchDate.getDate() + 30);

    function updateCountdown() {
        const now = new Date().getTime();
        const distance = launchDate - now;

        const days = Math.floor(distance / (1000 * 60 * 60 * 24));
        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);

        document.querySelector('.countdown-days').textContent = String(days).padStart(2, '0');
        document.querySelector('.countdown-hours').textContent = String(hours).padStart(2, '0');
        document.querySelector('.countdown-minutes').textContent = String(minutes).padStart(2, '0');
        document.querySelector('.countdown-seconds').textContent = String(seconds).padStart(2, '0');

        if (distance < 0) {
            clearInterval(countdownTimer);
            document.querySelector('.countdown').innerHTML = "وب‌سایت به زودی در دسترس خواهد بود";
        }
    }

    // Update countdown every second
    updateCountdown();
    const countdownTimer = setInterval(updateCountdown, 1000);
});
</script>
@endsection