<!-- Bootstrap 5 CSS -->
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

<!-- Custom CSS -->
<style>
    :root {
        --turquoise: #40E0D0;
        --turquoise-light: #7FFFD4;
    }

    .bg-turquoise {
        background-color: var(--turquoise);
    }

    .text-turquoise {
        color: var(--turquoise);
    }

    .btn-turquoise {
        background-color: var(--turquoise);
        border-color: var(--turquoise);
        color: white;
    }

    .btn-turquoise:hover {
        background-color: var(--turquoise-light);
        border-color: var(--turquoise-light);
    }

    /* Additional secondary color styles */
    .border-turquoise {
        border-color: var(--turquoise) !important;
    }

    .bg-turquoise-light {
        background-color: var(--turquoise-light);
    }

    /* Links */
    a.text-turquoise:hover {
        color: var(--turquoise-light);
    }

    /* Card hover effects */
    .card.hover-turquoise:hover {
        border-color: var(--turquoise);
        box-shadow: 0 0 15px rgba(64, 224, 208, 0.3);
    }
</style>
