@extends('layouts.app')

@section('title', __('messages.investment_services_meta_title'))

@section('meta_description', __('messages.investment_services_meta_description'))

@section('meta_keywords', __('messages.investment_services_meta_keywords'))

@section('content')
    <!-- Page Header -->
    <header class="bg-turquoise-light py-5">
        <div class="container">
            <h1 class="display-4 fw-bold text-dark">{{ __('messages.investment_title') }}</h1>
            <p class="lead">{{ __('messages.investment_subtitle') }}</p>
        </div>
    </header>

    <!-- Service Content -->
    <section class="py-5">
        <div class="container">
            <div class="row mb-5">
                <div class="col-lg-8 mx-auto">
                    <div class="service-icon mb-4 text-center">
                        <i class="fas fa-chart-line fa-3x text-turquoise"></i>
                    </div>
                    <p class="lead mb-4 text-center">{{ __('messages.investment_description') }}</p>
                </div>
            </div>

            <!-- Key Features -->
            <div class="row mb-5">
                <div class="col-lg-8 mx-auto">
                    <h2 class="h3 mb-4 text-center">{{ __('messages.our_expertise') }}</h2>
                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <div class="card h-100 border-0 shadow-sm hover-card">
                                <div class="card-body p-4">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="icon-box bg-turquoise-light rounded-circle me-3">
                                            <i class="fas fa-chess text-turquoise"></i>
                                        </div>
                                        <h3 class="h5 mb-0">{{ __('messages.investment_features.strategy') }}</h3>
                                    </div>
                                    <p class="card-text text-muted">{{ __('messages.investment_strategy') }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-4">
                            <div class="card h-100 border-0 shadow-sm hover-card">
                                <div class="card-body p-4">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="icon-box bg-turquoise-light rounded-circle me-3">
                                            <i class="fas fa-money-bill-wave text-turquoise"></i>
                                        </div>
                                        <h3 class="h5 mb-0">{{ __('messages.investment_features.capital') }}</h3>
                                    </div>
                                    <p class="card-text text-muted">{{ __('messages.capital_raising') }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-4">
                            <div class="card h-100 border-0 shadow-sm hover-card">
                                <div class="card-body p-4">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="icon-box bg-turquoise-light rounded-circle me-3">
                                            <i class="fas fa-handshake text-turquoise"></i>
                                        </div>
                                        <h3 class="h5 mb-0">{{ __('messages.investment_features.relations') }}</h3>
                                    </div>
                                    <p class="card-text text-muted">{{ __('messages.investor_relations') }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-4">
                            <div class="card h-100 border-0 shadow-sm hover-card">
                                <div class="card-body p-4">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="icon-box bg-turquoise-light rounded-circle me-3">
                                            <i class="fas fa-seedling text-turquoise"></i>
                                        </div>
                                        <h3 class="h5 mb-0">{{ __('messages.investment_features.planning') }}</h3>
                                    </div>
                                    <p class="card-text text-muted">{{ __('messages.growth_planning') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Image Section -->
            <div class="row mb-5">
                <div class="col-lg-10 mx-auto">
                    <img src="{{ asset('images/services/investment-services.jpg') }}" alt="{{ __('messages.investment_title') }}" class="img-fluid rounded shadow">
                </div>
            </div>

            <!-- Call to Action -->
            <div class="row">
                <div class="col-lg-8 mx-auto text-center">
                    <div class="bg-turquoise-light p-5 rounded shadow">
                        <h2 class="h3 mb-4">{{ __('messages.ready_to_start') }}</h2>
                        <p class="mb-4">{{ __('messages.contact_description') }}</p>
                        <a href="{{ route('contact') }}" class="btn btn-turquoise btn-lg px-5">
                            {{ __('messages.contact_us_button') }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection

@push('styles')
<style>
    .bg-turquoise-light {
        background-color: rgba(64, 224, 208, 0.1);
    }

    .text-turquoise {
        color: #40E0D0;
    }

    .btn-turquoise {
        background-color: #40E0D0;
        border-color: #40E0D0;
        color: white;
    }

    .btn-turquoise:hover {
        background-color: #3AC5B7;
        border-color: #3AC5B7;
        color: white;
    }

    .icon-box {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .hover-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .hover-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
    }
</style>
@endpush
