/* Import statements must come first */
@import 'bootstrap/dist/css/bootstrap.min.css';

/* Persian Font Setup */
body {
    font-family: Vazir, -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
}

/* Adjust line height for Persian text */
html[lang="fa"] {
    line-height: 1.8;
}

/* RTL dropdown menu alignment for Persian language */
html[dir="rtl"] .dropdown-menu {
    text-align: right;
    left: auto;
    right: 0;
}

html[dir="rtl"] .dropdown-item {
    text-align: right !important;
    direction: rtl !important;
    padding-right: 1rem !important;
    padding-left: 1rem !important;
    display: block;
    width: 100%;
}

/* Fix spacing for nav items in RTL mode */
html[dir="rtl"] .nav-item.ms-3 {
    margin-left: 0 !important;
    margin-right: 1rem !important;
}

/* Fix dropdown menu item alignment in RTL mode */
html[dir="rtl"] .dropdown-menu .dropdown-item::before {
    float: right;
    margin-left: 0.5rem;
    margin-right: -0.5rem;
}

/* Ensure dropdown toggle caret is properly positioned in RTL */
html[dir="rtl"] .dropdown-toggle::after {
    margin-right: 0.255em;
    margin-left: 0;
}

/* Font weights */
h1, h2, h3, h4, h5, h6 {
    font-family: Vazir;
    font-weight: 700;
}

.font-medium {
    font-weight: 500;
}

.font-light {
    font-weight: 300;
}

/* Your custom styles here */
.service-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    overflow: hidden;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.service-icon {
    width: 70px;
    height: 70px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.bg-turquoise {
    background-color: #40E0D0;
}

.text-turquoise {
    color: #40E0D0;
}

/* Add hover effect */
.service-card:hover .service-icon {
    transform: scale(1.1);
    transition: transform 0.3s ease;
}
