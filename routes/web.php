<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ContactController;

Route::get('/', function () {
    return view('welcome');
})->name('home');

Route::get('/services', function () {
    return view('services');
})->name('services');

// Service Pages Routes
Route::get('/services/business', function () {
    return view('services.business');
})->name('services.business');

Route::get('/services/financial', function () {
    return view('services.financial');
})->name('services.financial');

Route::get('/services/transport', function () {
    return view('services.transport');
})->name('services.transport');

Route::get('/services/investment', function () {
    return view('services.investment');
})->name('services.investment');

Route::get('/services/hr', function () {
    return view('services.hr');
})->name('services.hr');

Route::get('/services/insurance', function () {
    return view('services.insurance');
})->name('services.insurance');

Route::get('/about', function () {
    return view('about');
})->name('about');

Route::get('/contact', function () {
    return view('contact');
})->name('contact');

Route::post('/contact', [ContactController::class, 'store'])->name('contact.store');

// اضافه کردن مسیر جدید برای case studies
Route::get('/case-studies', function () {
    return view('case-studies');
})->name('case-studies');

Route::get('language/{locale}', function ($locale) {
    if (in_array($locale, config('app.available_locales'))) {
        app()->setLocale($locale);
        session()->put('locale', $locale);
    } else {
        // If invalid locale, use default
        app()->setLocale(config('app.locale'));
        session()->put('locale', config('app.locale'));
    }
    return redirect()->back();
})->name('language.switch');
