<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>به زودی - لیان تراز</title>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Bootstrap RTL -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css">
    <!-- Vazir Font -->
    <link href="https://cdn.jsdelivr.net/gh/rastikerdar/vazir-font@v30.1.0/dist/font-face.css" rel="stylesheet">

    <style>
        body {
            font-family: Vazir, sans-serif;
            margin: 0;
            padding: 0;
            min-height: 100vh;
            background: linear-gradient(135deg, #000080 0%, #40E0D0 100%);
            overflow-x: hidden;
        }

        .pattern-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('data:image/svg+xml,<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><rect x="0" y="0" width="1" height="1" fill="rgba(255,255,255,0.1)"/></svg>');
            opacity: 0.1;
            pointer-events: none;
        }

        .coming-soon {
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
            padding: 2rem 0;
        }

        .countdown-box {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 1rem;
            padding: 1.5rem;
            backdrop-filter: blur(10px);
        }

        .countdown-number {
            font-size: 3rem;
            font-weight: bold;
            color: white;
            margin: 0;
            line-height: 1;
        }

        .countdown-label {
            color: rgba(255, 255, 255, 0.8);
            margin: 0;
        }

        .social-link {
            width: 45px;
            height: 45px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            background: white;
            border-radius: 50%;
            color: #000080;
            text-decoration: none;
            margin: 0 0.5rem;
            transition: transform 0.3s ease;
        }

        .social-link:hover {
            transform: translateY(-3px);
            color: #40E0D0;
        }

        .newsletter-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
            padding: 1rem;
        }

        .newsletter-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .btn-subscribe {
            background: white;
            color: #000080;
            border: none;
            padding: 1rem 2rem;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .btn-subscribe:hover {
            background: #40E0D0;
            color: white;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-fadeInUp {
            animation: fadeInUp 0.6s ease forwards;
        }

        .delay-1 { animation-delay: 0.2s; }
        .delay-2 { animation-delay: 0.4s; }
        .delay-3 { animation-delay: 0.6s; }
        .delay-4 { animation-delay: 0.8s; }
    </style>
</head>
<body>
    <div class="pattern-overlay"></div>

    <div class="coming-soon">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8 text-center">
                    <!-- Logo -->
                    <img src="logo.png" alt="لیان طراز" class="mb-5 animate-fadeInUp" style="max-height: 80px;">

                    <!-- Main Content -->
                    <h1 class="display-4 fw-bold text-white mb-4 animate-fadeInUp delay-1">
                        لیان طراز متوازن
                        <span class="d-block">پیشرو در تجارت بین‌الملل</span>
                    </h1>

                    <p class="lead text-white-50 mb-5 animate-fadeInUp delay-2">
                        ۲۰ سال تجربه در خدمات جامع بازرگانی
                        <br>
                        صادرات، واردات و ترخیص با تضمین کیفیت برتر
                    </p>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // تنظیم تاریخ راه‌اندازی (مثال: 30 روز دیگر)
            const launchDate = new Date();
            launchDate.setDate(launchDate.getDate() + 1);

            function updateCountdown() {
                const now = new Date().getTime();
                const distance = launchDate - now;

                const days = Math.floor(distance / (1000 * 60 * 60 * 24));
                const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((distance % (1000 * 60)) / 1000);

                document.getElementById('days').textContent = String(days).padStart(2, '0');
                document.getElementById('hours').textContent = String(hours).padStart(2, '0');
                document.getElementById('minutes').textContent = String(minutes).padStart(2, '0');
                document.getElementById('seconds').textContent = String(seconds).padStart(2, '0');

                if (distance < 0) {
                    clearInterval(countdownTimer);
                    document.querySelector('.countdown').innerHTML = "وب‌سایت به زودی در دسترس خواهد بود";
                }
            }

            // بروزرسانی شمارنده هر ثانیه
            updateCountdown();
            const countdownTimer = setInterval(updateCountdown, 1000);
        });
    </script>
</body>
</html>
